const express = require('express');
const app = express();
const puppeteer = require('puppeteer');
const { v4: uuidv4 } = require('uuid');
const COS = require('cos-nodejs-sdk-v5');

// 配置 Puppeteer 参数
const options = {
  headless: 'new',
  executablePath: "/home/<USER>/.cache/puppeteer/chrome/linux-115.0.5790.98/chrome-linux64/chrome",
  args: [
    '--disable-extensions',
    '--disable-plugins',
    '--disable-setuid-sandbox',
    '--no-sandbox',
    '--no-zygote',
    '--disable-setuid-sandbox',
    '--disable-dev-shm-usage',
    '--no-first-run',
    '--disable-background-networking',
    '--disable-sync',
    '--disable-translate',
    '--hide-scrollbars',
    '--metrics-recording-only',
    '--mute-audio',
    '--safebrowsing-disable-auto-update',
    '--ignore-certificate-errors',
    '--ignore-ssl-errors',
    '--ignore-certificate-errors-spki-list',
    '--font-render-hinting=medium',
    '--memory-pressure-off',
    '--max_old_space_size=4096'
  ],
};

// 浏览器实例管理
let browser = null;
let pageCount = 0;
const MAX_PAGES = 50; // 最大页面数，超过后重启浏览器
const waitingQueue = []; // 等待队列

async function getBrowser() {
  if (!browser || browser.process() === null) {
    browser = await puppeteer.launch(options);
    pageCount = 0;
  }
  return browser;
}

async function restartBrowser() {
  if (browser) {
    try {
      await browser.close();
    } catch (error) {
      console.error('Error closing browser:', error);
    }
  }
  browser = await puppeteer.launch(options);
  pageCount = 0;
  // 重启后通知等待的请求
  notifyWaitingRequests();
}

// 等待页面可用
async function waitForAvailablePage(timeout = 10000) {
  return new Promise((resolve, reject) => {
    // 如果当前页面数小于最大值，直接返回
    if (pageCount < MAX_PAGES) {
      resolve();
      return;
    }

    // 设置超时
    const timeoutId = setTimeout(() => {
      // 从等待队列中移除
      const index = waitingQueue.indexOf(resolve);
      if (index > -1) {
        waitingQueue.splice(index, 1);
      }
      reject(new Error('等待页面超时'));
    }, timeout);

    // 添加到等待队列
    const wrappedResolve = () => {
      clearTimeout(timeoutId);
      resolve();
    };
    waitingQueue.push(wrappedResolve);
  });
}

// 通知等待的请求
function notifyWaitingRequests() {
  while (waitingQueue.length > 0 && pageCount < MAX_PAGES) {
    const resolve = waitingQueue.shift();
    resolve();
  }
}

// 页面关闭时的清理函数
async function closePage(page) {
  if (page) {
    try {
      await page.close();
      pageCount--;
      // 页面关闭后通知等待的请求
      notifyWaitingRequests();
    } catch (error) {
      console.error('Error closing page:', error);
    }
  }
}

// 请求队列管理
const requestQueue = [];
let processing = false;
const MAX_CONCURRENT = 3; // 最大并发数

async function processQueue() {
  if (processing || requestQueue.length === 0) return;
  
  processing = true;
  const concurrent = [];
  
  while (requestQueue.length > 0 && concurrent.length < MAX_CONCURRENT) {
    concurrent.push(requestQueue.shift()());
  }
  
  await Promise.all(concurrent);
  processing = false;
  
  if (requestQueue.length > 0) {
    setImmediate(processQueue);
  }
}

// 配置 COS 参数
const cos = new COS({
  SecretId: "",
  SecretKey: "",
});

// 上传到 COS
function uploadToCos(filename, dataInfo) {
  return new Promise((resolve, reject) => {
    cos.putObject({
      Bucket: "dta-1252139118",
      Region: "ap-beijing",
      Key: filename,
      Body: dataInfo,
    },
    (err, data) => {
      if (err) {
        console.error(err);
        reject(err);
      } else {
        console.log(filename);
        resolve(`https://dta-1252139118.file.myqcloud.com/${filename}`);
      }
    });
  });
}

// 成功响应
function createSuccessResponse(pdfUrl, imageUrl) {
  return {
    status: true,
    code: 200,
    message: 'success',
    data: {
      pdf_url: pdfUrl || null, // 如果没有生成 PDF，返回 null
      image_url: imageUrl || null, // 如果没有生成图片，返回 null
    },
    redirect: '',
    lang: 'zh_CN',
  };
}

// 失败响应
function createFailResponse(code, message) {
  return {
    status: true,
    code: code,
    message: message,
    data: {},
    redirect: '',
    lang: 'zh_CN',
  };
}

// 处理截图任务
async function handleScreenshotTask(req, res) {
  let page = null;
  
  try {
    let url = req.query.url;
    let format = req.query.format || 'png';
    if(format === 'image'){
        format = 'png'
    }

    const quality = req.query.quality ? parseInt(req.query.quality) : 80;
    const scale = req.query.scale ? parseFloat(req.query.scale) : 1;

    try {
        if (url.includes('%')) {
          url = decodeURIComponent(url);
        }
      } catch (err) {
        console.error('Failed to decode URL:', url, err);
        return res.send(createFailResponse(400, 'Invalid URL encoding'));
      }

    // 检查是否需要重启浏览器
    if (pageCount >= MAX_PAGES) {
      await restartBrowser();
    }

    const browserInstance = await getBrowser();
    const defaultWidth = 1024;
    const defaultHeight = 768;

    let width = req.query.width ? Number(req.query.width) : defaultWidth;
    let height = req.query.height ? Number(req.query.height) : defaultHeight;

    // 打开一个新页面
    page = await browserInstance.newPage();
    pageCount++;

    // 设置页面超时
    page.setDefaultTimeout(30000);
    page.setDefaultNavigationTimeout(30000);

    await page.addStyleTag({
      content: `
        * {
          font-variant-numeric: tabular-nums !important;
          font-feature-settings: "tnum" on !important;
        }
      `
    });

    if (req.query.isMobile === 'true') {
      await page.emulate(puppeteer.devices['iPhone XR']);
    }

    await page.goto(url, {
      'timeout': 30000,
      waitUntil: 'networkidle0'
    });

    // 如果有参数 dom，就动态设置 viewport
    if (req.query.dom) {
      const dom = await page.$eval(req.query.dom, (x) => {
        return JSON.parse(JSON.stringify(window.getComputedStyle(x)));
      });
      width = Number(dom.width.slice(0, -2));
      height = Number(dom.height.slice(0, -2));
    }

    if (req.query.isMobile === 'true') {
      await page.setViewport({
        width: width,
        height: height,
        isMobile: true,
        hasTouch: true,
        deviceScaleFactor: 3
      });
    } else {
      await page.setViewport({
        width: width,
        height: height,
      });
    }

    let pdfUrl = '';
    let imageUrl = '';
    
    // 生成 PDF 文件
    if (format === 'pdf' || format === 'all') {
      const pdf = await page.pdf({
        scale: scale,
        printBackground: true,
        preferCSSPageSize: true,
        format: 'A4',
        '-webkit-print-color-adjust': 'exact'
      });
      const pdfFilename = `${uuidv4()}.pdf`;
      pdfUrl = await uploadToCos(pdfFilename, pdf);
    }

    // 生成图片文件
    if (format === 'png' || format === 'webp' || format === 'all' || format === 'image') {
        if (format === 'all' || format === 'image') {
            format = 'png';
        }

        const screenshot = await page.screenshot({
            fullPage: true,
            omitBackground: true,
            type: format
        });

        const imageFilename = `${uuidv4()}.${format}`;
        imageUrl = await uploadToCos(imageFilename, screenshot);
    }

    res.send(createSuccessResponse(pdfUrl, imageUrl));

  } catch (error) {
    console.error('Screenshot error:', error);
    res.send(createFailResponse(500, error.message));
  } finally {
    // 确保页面被关闭
    if (page) {
      try {
        await page.close();
      } catch (error) {
        console.error('Error closing page:', error);
      }
    }
  }
}

// 处理 GET 请求
app.get('/screenshot', async (req, res) => {
  return new Promise((resolve) => {
    requestQueue.push(async () => {
      await handleScreenshotTask(req, res);
      resolve();
    });
    processQueue();
  });
});

// 健康检查接口
app.get('/health', (req, res) => {
  res.json({
    status: 'ok',
    pageCount: pageCount,
    queueLength: requestQueue.length,
    memory: process.memoryUsage()
  });
});

// 定期清理内存
setInterval(async () => {
  if (global.gc) {
    global.gc();
  }
  
  // 定期重启浏览器
  if (pageCount > MAX_PAGES * 0.8) {
    console.log('Restarting browser due to high page count');
    await restartBrowser();
  }
}, 60000); // 每分钟检查一次

// 优雅关闭
process.on('SIGTERM', async () => {
  console.log('Received SIGTERM, closing browser...');
  if (browser) {
    await browser.close();
  }
  process.exit(0);
});

process.on('SIGINT', async () => {
  console.log('Received SIGINT, closing browser...');
  if (browser) {
    await browser.close();
  }
  process.exit(0);
});

// 启动服务器
const port = 9000;
app.listen(port, () => {
  console.log(`Server started on port ${port}`);
});
